/**
 * Vehicle Service
 * Handles vehicle-related operations and data retrieval
 */

import Vehicle from '../models/Vehicle.js';
import VehicleModel from '../models/VehicleModel.js';
import VehicleMaker from '../models/VehicleMaker.js';
import VehicleType from '../models/VehicleType.js';
import logger from '../utils/logger.js';

/**
 * Get comprehensive vehicle information by vehicle ID
 * @param {string} vehicleId - The vehicle ID to fetch information for
 * @returns {Object|null} Vehicle information object or null if not found
 */
export const getVehicleInfoById = async (vehicleId) => {
  try {
    const vehicle = await Vehicle.findById(vehicleId);
    
    if (!vehicle) {
      logger.warn('Vehicle not found', { vehicleId });
      return null;
    }

    logger.info("vehicle model id :", { vehicle: vehicle.vehical_model });
    VehicleModel.findById(vehicle.vehical_model).then((data) => {
      logger.info("VehicleModel data:", { data });
    });

    const vehicleInfo = {
      vehicle_has_carrier: vehicle.carrier,
      vehicle_make: null,
      vehicle_model: null,
      vehicle_type: null
    };

    // Fetch vehicle model and make in parallel
    const [vehicleModel, vehicleMaker] = await Promise.all([
      VehicleModel.findById(vehicle.vehical_model),
      VehicleMaker.findById(vehicle.vehical_make),
    ]);

    if (vehicleModel) {
      vehicleInfo.vehicle_model = vehicleModel.model;

      const vehicleType = await VehicleType.findById(vehicleModel.type_id);
      if (vehicleType) {
        vehicleInfo.vehicle_type = vehicleType.name;
      }
    }

    // Overwrite make only if it exists in the maker (optional: remove this if `vehicleType` should take precedence)
    if (vehicleMaker) {
      vehicleInfo.vehicle_make = vehicleMaker.maker;
    }

    logger.info('Vehicle information retrieved successfully', { 
      vehicleId, 
      vehicleInfo 
    });

    return vehicleInfo;
  } catch (error) {
    logger.warn("Failed to fetch vehicle information", {
      vehicleId,
      error: error.message,
    });
    return null;
  }
};

export default {
  getVehicleInfoById
};
