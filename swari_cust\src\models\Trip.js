/**
 * Trip Model
 * Defines the schema for trip data
 * PRD Reference: Sections 4.2, 10.3
 */

import mongoose from 'mongoose';

const TripSchema = new mongoose.Schema({
  customer_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
    required: true
  },
  trip_type: {
    type: String,
    required: true,
    enum: ['one-way', 'round-trip', 'custom-days'],
    default: 'one-way'
  },
  pickup_coords: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true
    }
  },
  dropoff_coords: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true
    }
  },
  pickup_address: {
    type: String,
    required: true
  },
  dropoff_address: {
    type: String,
    required: true
  },
  date_time: {
    type: Date,
    required: true
  },
  return_date_time: {
    type: Date,
    // Required only for round-trip
  },
  cancel_by:{
    type: Date,
    required: true
  },
  custom_days: [{
    type: Date
    // Used only for custom-days trip type
  }],
  car_type: {
    type: String,
    required: true,
    enum: ['suv', 'sedan', 'hatchback', 'van']
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'accepted', 'in-progress', 'completed', 'cancelled'],
    default: 'pending'
  },
  bid_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Bid',
    default: null // Assuming Bid model exists and can be referenced
  },
  bid_amount: { // Add bid_amount field
    type: Number,
    default: null
  },
  driver_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  notes: {
    type: String
  },
  distance: {
    type: Number,
    default: null // Distance in kilometers, optional field
  },
  vehicle_make: {
    type: String,
    default: null
  },
  vehicle_model: {
    type: String,
    default: null
  },
  vehicle_type: {
    type: String,
    default: null
  },
  vehicle_has_carrier: {
    type: Boolean,
    default: false
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } });

// Create geospatial indexes for location-based queries
TripSchema.index({ pickup_coords: '2dsphere' });
TripSchema.index({ dropoff_coords: '2dsphere' });

const Trip = mongoose.model('CustomerTrip', TripSchema);
export default Trip;