<div class="login-page animate" >
    <div class="row">
        <div class="col-lg-4 col-lg-offset-4 border-shedow">
            <img src="../img/icon.png" height="150" width="150" class="user-avatar" />
            <h1>Swari</h1>
            <form >
                <div class="form-content">
                    <div class="form-group">
                        
                        <input type="text" ng-model="user.phone_number" class="form-control input-underline input-lg" name="usrname" placeholder="Mobile Number" >
                     
                    </div>
                    <div class="form-group">
                        <input type="password" ng-model="user.password" class="form-control input-underline input-lg" name="password" placeholder="Password" >
                       
                    </div>
                </div>
                <button ng-if='!loading' class="btn rounded-btn" ng-click="loginAdmin(user)"> Log in </button>
                <button ng-if='loading' class="btn rounded-btn" style="color: gray"> Logging In </button>
                <button class="btn rounded-btn" ng-click="forgotPassword(user)"> Forgot Password </button>
                <button class="btn rounded-btn" ng-click="goToRegister()"> Register New Admin </button>
            </form>
            <!-- <div ng-show="forgotPass">
              <div class="text-center padding-bottom">{{ forgotMessage }}</div>
              <button class="btn rounded-btn" ng-click="sendPassword();"> Send Password </button>
            </div> -->
        </div>	
    </div>
</div>

