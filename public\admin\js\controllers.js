angular.module('app.controllers', ['ui.bootstrap', 'APIModule'])

.controller('AppCtrl', function($scope, APIService, $state) {
  $scope.logout = function() {
    localStorage.removeItem("UserDeatails");
    localStorage.removeItem("token");
    $scope.user = {};
      $state.go('login');
    
  }




  
  
})

.controller('AdminController', function($scope, $state, APIService) {
    $scope.newAdmin = {};
    $scope.loading = false;
    
    $scope.registerAdmin = function(admin) {
        $scope.loading = true;
        APIService.setData({ 
            req_url: PrefixUrl + '/api/admin/create', 
            data: admin 
        }).then(function(res) {
            if (res.data.message === 'Admin created successfully') {
                alert('Admin registered successfully');
                $scope.newAdmin = {};
                $state.go("login"); // Redirect to login after successful registration
            } else {
                alert('Registration failed: ' + res.data.message);
            }
            $scope.loading = false;
        }, function(err) {
            alert('Error: ' + (err.data.message || 'Unknown error'));
            $scope.loading = false;
        });
    };
    
    $scope.goToLogin = function() {
        $state.go("login");
    };
});
