<div class="register-page animate">
    <div class="row">
        <div class="col-lg-4 col-lg-offset-4 border-shedow">
            <h1>Admin Registration</h1>

            <!-- Success Message -->
            <div ng-if="message" class="alert alert-success" style="margin-bottom: 20px;">
                {{ message }}
            </div>

            <!-- Error Message -->
            <div ng-if="error" class="alert alert-danger" style="margin-bottom: 20px;">
                {{ error }}
            </div>

            <form>
                <div class="form-content">
                    <div class="form-group">
                        <input type="text" ng-model="admin.username" class="form-control input-underline input-lg" placeholder="Username" required>
                    </div>
                    <div class="form-group">
                        <input type="email" ng-model="admin.email" class="form-control input-underline input-lg" placeholder="Email" required>
                    </div>
                    <div class="form-group">
                        <input type="password" ng-model="admin.password" class="form-control input-underline input-lg" placeholder="Password" required>
                    </div>
                    <div class="form-group">
                        <select ng-model="admin.role" class="form-control input-underline input-lg">
                            <option value="">Select Role</option>
                            <option value="admin">Admin</option>
                            <option value="super_admin">Super Admin</option>
                        </select>
                    </div>
                </div>
                <button ng-if='!loading' class="btn rounded-btn" ng-click="registerAdmin(admin)">Register</button>
                <button ng-if='loading' class="btn rounded-btn" style="color: gray">Registering...</button>
                <button class="btn rounded-btn" ng-click="goToLogin()">Back to Login</button>
            </form>
        </div>
    </div>
</div>