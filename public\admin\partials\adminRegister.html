<div class="register-page animate">
    <div class="row">
        <div class="col-lg-4 col-lg-offset-4 border-shedow">
            <h1>Admin Registration</h1>
            <form>
                <div class="form-content">
                    <div class="form-group">
                        <input type="text" ng-model="newAdmin.username" class="form-control input-underline input-lg" placeholder="Username">
                    </div>
                    <div class="form-group">
                        <input type="email" ng-model="newAdmin.email" class="form-control input-underline input-lg" placeholder="Email">
                    </div>
                    <div class="form-group">
                        <input type="password" ng-model="newAdmin.password" class="form-control input-underline input-lg" placeholder="Password">
                    </div>
                    <div class="form-group">
                        <select ng-model="newAdmin.role" class="form-control input-underline input-lg">
                            <option value="admin">Admin</option>
                            <option value="super_admin">Super Admin</option>
                        </select>
                    </div>
                </div>
                <button ng-if='!loading' class="btn rounded-btn" ng-click="registerAdmin(newAdmin)">Register</button>
                <button ng-if='loading' class="btn rounded-btn" style="color: gray">Registering...</button>
                <button class="btn rounded-btn" ng-click="goToLogin()">Back to Login</button>
            </form>
        </div>
    </div>
</div>