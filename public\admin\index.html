<!DOCTYPE html>
<html lang="en" ng-app="triva">
  <head>
    <title>Swari</title>
        
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0"> -->
   <meta name="description" content="Taxi Connecting Pannel.">
    <meta charset="utf-8">

     <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Swari">

    <!-- Twitter -->
    <meta name="twitter:site" content="@Swari">
    <meta name="twitter:creator" content="@Swari">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Swari">
    <meta name="twitter:description" content="Taxi Connecting Pannel.">


    <!-- Facebook -->
   
    <meta property="og:description" content="Taxi Connecting Pannel.">
 
 
    <meta property="og:image:type" content="image/png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="600">

    <!-- Meta -->
    <meta name="description" content="Taxi Connecting Pannel.">
    <meta name="author" content="Swari">
    <link href="lib/font-awesome/css/font-awesome.css" rel="stylesheet">
    <link href="lib/Ionicons/css/ionicons.css" rel="stylesheet">
    <link href="lib/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet">
    <link href="lib/jquery-switchbutton/jquery.switchButton.css" rel="stylesheet">
    <link href="lib/rickshaw/rickshaw.min.css" rel="stylesheet">
    <link href="lib/chartist/chartist.css" rel="stylesheet">

  
     <script src="lib/jquery/jquery.js"></script>
    <script src="lib/popper.js/popper.js"></script>
    <script src="lib/bootstrap/bootstrap.js"></script>
    <script src="lib/perfect-scrollbar/js/perfect-scrollbar.jquery.js"></script>
    <script src="lib/moment/moment.js"></script>
    <script src="lib/jquery-ui/jquery-ui.js"></script>
    <script src="lib/jquery-switchbutton/jquery.switchButton.js"></script>
    <script src="lib/peity/jquery.peity.js"></script>
    <script src="lib/chartist/chartist.js"></script>
    <script src="lib/jquery.sparkline.bower/jquery.sparkline.min.js"></script>
    <script src="lib/d3/d3.js"></script>
    <script src="lib/rickshaw/rickshaw.min.js"></script>

    <script src="js/bracket.js"></script>
    <script src="js/ResizeSensor.js"></script>
    <!-- <script type="js/chart.rickshaw.js"></script> -->
    <!-- <script src="js/dashboard.js"></script> -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.13.0/css/all.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.13.0/css/v4-shims.css">
    <script defer src="https://use.fontawesome.com/releases/v5.13.0/js/all.js"></script>
    <script defer src="https://use.fontawesome.com/releases/v5.13.0/js/v4-shims.js"></script>


  
  
  <!-- Bracket CSS -->
    <link rel="stylesheet" href="css/bracket.css">
    
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <link href="css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="css/custom.css">

    <link rel="stylesheet" href="css/angular-datepicker.css">

    <!-- ui tree css -->
    <link rel="stylesheet" href="css/angular-ui-tree.min.css">
    <link rel="stylesheet" href="css/skin.min.css">
    <link rel="stylesheet" href="css/content.min.css">
    <link rel="stylesheet" href="css/content.css">
    <link rel="stylesheet" href="css/textAngular.css">
    <link href="lib/datatables/jquery.dataTables.css" rel="stylesheet">
    <link href="lib/select2/css/select2.min.css" rel="stylesheet">

    <!-- Notification popup css -->
    <!-- <link rel="stylesheet" href="css/ngDialog.min.css">
    <link rel="stylesheet" href="css/ngDialog-theme-default.min.css"> -->

    <script src="lib/angular.min.js"></script>
    <script src="lib/angular-ui-router.min.js"></script>

    <script src="lib/ui-bootstrap-tpls-1.3.2.min.js"></script>

    <!-- file upload library -->
    <!-- <script src="lib/ng-file-upload-shim.min.js"></script>
    <script src="lib/ng-file-upload.min.js"></script> -->

    <!-- Notification popup -->
    <script type="text/javascript" src="lib/ngDialog.min.js"></script>

    <!-- angular loader -->
    <!-- <script type="text/javascript" src="lib/spin.min.js"></script>
    <script type="text/javascript" src="lib/angular-spinner.min.js"></script>
    <script type="text/javascript" src="lib/angular-loading-spinner.js">
    </script>
    <script type="text/javascript" src="lib/angular-ui-tree.min.js">
    </script> -->
   

      <!-- <script src="lib/angularfire/dist/angularfire.min.js"></script>
      <script src="lib/geofire/dist/geofire.min.js"></script> -->
     <!-- <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAT0-6dOhxklmZD1Rc9Fjuu-on2OZYrFKI"></script>-->
     
        
     <!-- <script src="http://maps.googleapis.com/maps/api/js?key=AIzaSyCLj7AHQGJ0duNY_go_DhvdzzIqXhrknh4&libraries=places"></script>-->

    <script src="js/app.js"></script>
    <script src="js/services.js"></script>
    <script src="js/controllers.js"></script>

    <script src="js/login.js"></script>
    <script src="js/subscriptionFees.js"></script>
    <script src="js/filter.js"></script>

    <script src="js/users.js"></script>
    <script src="js/trips.js"></script>
    <script src="js/referralEarning.js"></script>
    <script src="js/referralEarningByUser.js"></script>
    <script src="js/withdrawWallet.js"></script>
    <script src="js/payToBankRemarks.js"></script>
    <script src="js/rateReview.js"></script>
    <script src="js/addUser.js"></script>
    <script src="js/notificationlogEntries.js"></script>
    <script src="js/broadcastMessage.js"></script>
    <script src="js/userTrips.js"></script>
    <script src="js/categoryAdd.js"></script>
    <script src="js/addVehicleMaker.js"></script>
    <script src="js/newPayouts.js"></script>
    <script src="js/payoutCreatedReport.js"></script>
    <script src="js/referralUsers.js"></script>
    <script src="js/updateProfile.js"></script>
    <script src="js/transactions.js"></script>
    <script src="js/gatewayTransactions.js"></script>
    <script src="js/userBusinessProfile.js"></script>
    <script src="js/userSuspendRemarks.js"></script>
    <script src="js/viewPayout.js"></script>
    <script src="js/viewPayoutTransactions.js"></script>
    <script src="js/emailSettings.js"></script>
    <script src="js/reportedComments.js"></script>
    <script src="js/subAdminList.js"></script>
    <script src="js/userTracking.js"></script>
    

    <script src="js/vehicleList.js"></script>
    <script src="js/subscription.js"></script>
    <script src="js/tripDetails.js"></script>
    <script src="js/reports.js"></script>
    <script src="js/addUserByAdmin.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/vehicleDetails.js"></script>
    <script src="js/otp.js"></script>
    <script src="js/forgotPassword.js"></script>
    <script src="js/suspendedUsers.js"></script>
    <!-- <script src="js/notification.js"></script> -->
    <script src="js/angular-datepicker.js"></script>
    <script src="js/angular-simple-pagination.js"></script>
    <script src="js/Chart.min.js"></script>
    <script src="js/angular-chart.min.js"></script>
    <script src="js/tinymce.js"></script>
    <script src="js/tinymce.min.js"></script>
    <script src="js/theme.min.js"></script>
    <script src="js/demo.js"></script>
    <script src="js/textAngular-rangy.min.js"></script>
    <script src="js/textAngular-sanitize.min.js"></script>
    <script src="js/textAngular.min.js"></script>
    <script src="js/FileSaver.js"></script>
    <script src="js/userContacts.js"></script>
    <script src="js/viewUserContacts.js"></script>
    <script src="js/passengerTrips.js"></script>
    <script src="js/tripDetailsPassenger.js"></script>
    <script src="js/supportTicket.js"></script>
    <script src="js/supportTicketDetail.js"></script>
     <script src="js/states.js"></script>
     <script src="js/updateDateState.js"></script>
     <script src="js/supportTicketClose.js"></script>
     <script src="js/moment.js"></script>
     <script src="js/angular-moment.js"></script>
    <script src="js/resetPassword.js"></script>
    <script src="js/offers.js"></script>
    <script src="js/offerCategory.js"></script>
    <script src="js/viewOffers.js"></script>
    <script src="js/angular-base64.js"></script>
    <script src="js/offerRedeem.js"></script>
    <script src="js/aws-sdk.js"></script>
    <script src="js/aws.js"></script>
    <script src="js/aws-sdk.min.js"></script>
    <script src="js/aws-sdk-2.1.43.min.js"></script>
    <script src="js/aws-sdk-2.722.0.min.js"></script>
    

       <script>
      $(function(){
        'use strict'

        // FOR DEMO ONLY
        // menu collapsed by default during first page load or refresh with screen
        // having a size between 992px and 1299px. This is intended on this page only
        // for better viewing of widgets demo.
        $(window).resize(function(){
          minimizeMenu();
        });

        minimizeMenu();

        function minimizeMenu() {
          if(window.matchMedia('(min-width: 992px)').matches && window.matchMedia('(max-width: 1299px)').matches) {
            // show only the icons and hide left menu label by default
            $('.menu-item-label,.menu-item-arrow').addClass('op-lg-0-force d-lg-none');
            $('body').addClass('collapsed-menu');
            $('.show-sub + .br-menu-sub').slideUp();
          } else if(window.matchMedia('(min-width: 1300px)').matches && !$('body').hasClass('collapsed-menu')) {
            $('.menu-item-label,.menu-item-arrow').removeClass('op-lg-0-force d-lg-none');
            $('body').removeClass('collapsed-menu');
            $('.show-sub + .br-menu-sub').slideDown();
          }
        }
      });
    </script>
     

   
  </head>
<body>

    <!-- content will be injected based on route -->
    <div ui-view></div>
  </body>
</html>
