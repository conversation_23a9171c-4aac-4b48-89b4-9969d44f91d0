/**
 * Admin Management JavaScript
 * Handles admin registration and password change functionality
 * PRD Reference: Sections 4.11, 10.2
 */

angular.module('admin.controllers', [])

.controller('AdminController', function ($scope, $state, APIService, $timeout) {
    
    // Initialize scope variables
    $scope.admin = {};
    $scope.passwordChange = {};
    $scope.loading = false;
    $scope.message = '';
    $scope.error = '';

    /**
     * Register a new admin
     * Only super_admin can register new admins
     */
    $scope.registerAdmin = function (admin) {
        // Reset messages
        $scope.message = '';
        $scope.error = '';
        
        // Validate required fields
        if (!admin || !admin.username || !admin.email || !admin.password) {
            $scope.error = 'All fields are required';
            return;
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(admin.email)) {
            $scope.error = 'Please enter a valid email address';
            return;
        }

        // Validate password strength
        if (admin.password.length < 8) {
            $scope.error = 'Password must be at least 8 characters long';
            return;
        }

        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;
        if (!passwordRegex.test(admin.password)) {
            $scope.error = 'Password must contain at least one lowercase letter, one uppercase letter, and one number';
            return;
        }

        // Validate username
        const usernameRegex = /^[a-zA-Z0-9_]+$/;
        if (!usernameRegex.test(admin.username)) {
            $scope.error = 'Username can only contain letters, numbers, and underscores';
            return;
        }

        if (admin.username.length < 3 || admin.username.length > 30) {
            $scope.error = 'Username must be between 3 and 30 characters';
            return;
        }

        // Validate role if provided
        if (admin.role && !['admin', 'super_admin'].includes(admin.role)) {
            $scope.error = 'Role must be either admin or super_admin';
            return;
        }

        $scope.loading = true;
        
        // Prepare data
        const adminData = {
            username: admin.username.trim(),
            email: admin.email.toLowerCase().trim(),
            password: admin.password,
            role: admin.role || 'admin'
        };

        // Get token from localStorage
        const token = localStorage.getItem('token');
        if (!token) {
            $scope.error = 'Authentication required. Please login again.';
            $scope.loading = false;
            return;
        }

        // Make API call
        APIService.setData({
            req_url: PrefixUrl + '/api/admin/register',
            data: adminData
        }).then(function (response) {
            $scope.loading = false;
            
            if (response.data && response.data.message === 'Admin registered successfully') {
                $scope.message = 'Admin registered successfully!';
                $scope.admin = {}; // Reset form
                
                // Auto-hide success message after 5 seconds
                $timeout(function() {
                    $scope.message = '';
                }, 5000);
            } else {
                $scope.error = response.data.message || 'Registration failed';
            }
        }, function (error) {
            $scope.loading = false;
            console.error('Admin registration error:', error);
            
            if (error.data && error.data.message) {
                $scope.error = error.data.message;
            } else if (error.data && error.data.errors && error.data.errors.length > 0) {
                $scope.error = error.data.errors[0].msg;
            } else {
                $scope.error = 'Registration failed. Please try again.';
            }
        });
    };

    /**
     * Change admin password
     */
    $scope.changePassword = function (passwordData) {
        // Reset messages
        $scope.message = '';
        $scope.error = '';
        
        // Validate required fields
        if (!passwordData || !passwordData.currentPassword || !passwordData.newPassword) {
            $scope.error = 'Both current and new passwords are required';
            return;
        }

        // Validate new password strength
        if (passwordData.newPassword.length < 8) {
            $scope.error = 'New password must be at least 8 characters long';
            return;
        }

        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;
        if (!passwordRegex.test(passwordData.newPassword)) {
            $scope.error = 'New password must contain at least one lowercase letter, one uppercase letter, and one number';
            return;
        }

        // Check if passwords are different
        if (passwordData.currentPassword === passwordData.newPassword) {
            $scope.error = 'New password must be different from current password';
            return;
        }

        // Confirm password validation
        if (passwordData.newPassword !== passwordData.confirmPassword) {
            $scope.error = 'New password and confirm password do not match';
            return;
        }

        $scope.loading = true;
        
        // Prepare data
        const changePasswordData = {
            currentPassword: passwordData.currentPassword,
            newPassword: passwordData.newPassword
        };

        // Get token from localStorage
        const token = localStorage.getItem('token');
        if (!token) {
            $scope.error = 'Authentication required. Please login again.';
            $scope.loading = false;
            return;
        }

        // Make API call
        APIService.updateData({
            req_url: PrefixUrl + '/api/admin/change-password',
            data: changePasswordData
        }).then(function (response) {
            $scope.loading = false;
            
            if (response.data && response.data.message === 'Password changed successfully') {
                $scope.message = 'Password changed successfully!';
                $scope.passwordChange = {}; // Reset form
                
                // Auto-hide success message after 5 seconds
                $timeout(function() {
                    $scope.message = '';
                }, 5000);
            } else {
                $scope.error = response.data.message || 'Password change failed';
            }
        }, function (error) {
            $scope.loading = false;
            console.error('Password change error:', error);
            
            if (error.data && error.data.message) {
                $scope.error = error.data.message;
            } else if (error.data && error.data.errors && error.data.errors.length > 0) {
                $scope.error = error.data.errors[0].msg;
            } else {
                $scope.error = 'Password change failed. Please try again.';
            }
        });
    };

    /**
     * Clear messages
     */
    $scope.clearMessages = function() {
        $scope.message = '';
        $scope.error = '';
    };

    /**
     * Reset registration form
     */
    $scope.resetRegistrationForm = function() {
        $scope.admin = {};
        $scope.clearMessages();
    };

    /**
     * Reset password change form
     */
    $scope.resetPasswordForm = function() {
        $scope.passwordChange = {};
        $scope.clearMessages();
    };

});
