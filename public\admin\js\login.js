angular.module('login.controllers', [])

.controller('LoginController', function ($scope, $state, APIService) {

    $scope.user = {};
    $scope.loading = false;

    /**
     * Admin login function
     */
    $scope.loginAdmin = function (user) {
        if (!user || !user.phone_number || !user.password) {
            alert('Please enter both mobile number and password.');
            return;
        }

        $scope.loading = true;
        $scope.user = user;
        $scope.user.role = "admin";

        APIService.setData({
            req_url: PrefixUrl + '/user/login',
            data: $scope.user
        }).then(function (res) {
            console.log(res);

            if (res.data.user_details && res.data.user_details.suspend == true) {
                alert("Your account is suspended please contact admin");
                $scope.loading = false;
                return;
            }

            if (!res.data.message) {
                // Successful login
                localStorage.setItem('UserDeatails', JSON.stringify(res.data));
                localStorage.setItem('token', res.data.token);
                console.log("Login successful", localStorage.getItem('UserDeatails'));
                $scope.user = {};
                $state.go("app.UserDetails");
            } else {
                console.log("Login failed");
                alert('Enter valid username or password.');
                $scope.user = {};
                $scope.loading = false;
            }
        }, function (error) {
            console.log('Login error:', error);
            alert(error.data && error.data.msg ? error.data.msg : 'Login failed. Please try again.');
            $scope.loading = false;
        });
    };

    /**
     * Navigate to forgot password page
     */
    $scope.forgotPassword = function(){
        $state.go("forgotPassword");
    };

    /**
     * Navigate to admin registration page
     */
    $scope.goToRegister = function(){
        $state.go("app.adminRegister");
    };
});
