angular.module('login.controllers', [])

.controller('LoginController', function ($scope, $state, APIService) {
   
    $scope.user = {};
    $scope.loading = false;
    
    // Existing login functions...
    
    $scope.forgotPassword = function(){
        $state.go("forgotPassword");
    }
    
    // Add this new function for registration navigation
    $scope.goToRegister = function(){
        $state.go("app.adminRegister");
    }
});
